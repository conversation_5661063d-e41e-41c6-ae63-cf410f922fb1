# CycloneDDS 使用说明

## 快速开始

### 1. 一键安装（推荐）
```bash
# 运行快速开始脚本
./quick_start.sh
```

### 2. 分步安装
```bash
# 步骤1：安装 CycloneDDS
./install_cyclonedds.sh

# 步骤2：测试安装
./test_cyclonedds.sh
```

## 文件说明

### 脚本文件
- `quick_start.sh` - 一键安装和配置脚本
- `install_cyclonedds.sh` - 自动安装脚本
- `test_cyclonedds.sh` - 测试和验证脚本

### 配置文件
- `cyclonedds_configs/default.xml` - 默认配置
- `cyclonedds_configs/high_performance.xml` - 高性能配置
- `cyclonedds_configs/debug.xml` - 调试配置
- `cyclonedds_configs/multi_machine.xml` - 多机器通信配置

### 文档文件
- `README.md` - 详细安装和使用指南
- `使用说明.md` - 本文件

## 环境变量

安装完成后，以下环境变量会自动设置：

```bash
export RMW_IMPLEMENTATION=rmw_cyclonedds_cpp
export CYCLONEDDS_URI=file:///tmp/cyclonedds.xml
export CYCLONEDDS_VERBOSITY=1
```

## 基本使用

### 1. 启动 ROS2 节点
```bash
# 启动 talker 节点
ros2 run demo_nodes_cpp talker

# 启动 listener 节点
ros2 run demo_nodes_cpp listener
```

### 2. 查看系统状态
```bash
# 查看话题
ros2 topic list

# 查看节点
ros2 node list

# 查看服务
ros2 service list
```

### 3. 切换配置文件
```bash
# 使用高性能配置
export CYCLONEDDS_URI=file://$(pwd)/cyclonedds_configs/high_performance.xml

# 使用调试配置
export CYCLONEDDS_URI=file://$(pwd)/cyclonedds_configs/debug.xml
```

## 故障排除

### 常见问题

#### 1. 找不到 CycloneDDS 库
```bash
# 重新加载库缓存
sudo ldconfig

# 检查库文件
ldconfig -p | grep cyclonedds
```

#### 2. ROS2 无法使用 CycloneDDS
```bash
# 检查环境变量
echo $RMW_IMPLEMENTATION

# 重新设置环境变量
export RMW_IMPLEMENTATION=rmw_cyclonedds_cpp
```

#### 3. 网络通信问题
```bash
# 检查网络接口
ip addr show

# 检查防火墙
sudo ufw status
```

### 调试技巧

#### 启用详细日志
```bash
export CYCLONEDDS_VERBOSITY=5
```

#### 检查 DDS 发现
```bash
# 如果安装了 cdds 工具
cdds ls
```

## 性能优化

### 1. 使用高性能配置
```bash
export CYCLONEDDS_URI=file://$(pwd)/cyclonedds_configs/high_performance.xml
```

### 2. 调整系统参数
```bash
# 增加网络缓冲区大小
sudo sysctl -w net.core.rmem_max=26214400
sudo sysctl -w net.core.wmem_max=26214400
```

### 3. 禁用不必要的服务
```bash
# 禁用 IPv6（如果不需要）
sudo sysctl -w net.ipv6.conf.all.disable_ipv6=1
```

## 多机器部署

### 1. 配置网络
修改 `cyclonedds_configs/multi_machine.xml` 中的 IP 地址：
```xml
<NetworkInterfaceAddress>*************</NetworkInterfaceAddress>
```

### 2. 设置环境变量
在所有机器上设置相同的环境变量：
```bash
export RMW_IMPLEMENTATION=rmw_cyclonedds_cpp
export CYCLONEDDS_URI=file:///path/to/cyclonedds_configs/multi_machine.xml
```

### 3. 测试连接
```bash
# 机器1：启动 talker
ros2 run demo_nodes_cpp talker

# 机器2：启动 listener
ros2 run demo_nodes_cpp listener
```

## 更新和维护

### 更新 CycloneDDS
```bash
# 重新运行安装脚本
./install_cyclonedds.sh
```

### 清理构建文件
```bash
cd ~/cyclonedds/build
make clean
```

## 参考资源

- [CycloneDDS 官方文档](https://cyclonedds.io/docs/)
- [ROS2 DDS 调优指南](https://docs.ros.org/en/foxy/Guides/DDS-tuning.html)
- [CycloneDDS GitHub 仓库](https://github.com/eclipse-cyclonedds/cyclonedds)

## 技术支持

如果遇到问题，请：

1. 运行测试脚本：`./test_cyclonedds.sh`
2. 查看详细日志：`export CYCLONEDDS_VERBOSITY=5`
3. 参考 README.md 中的故障排除部分
4. 在 GitHub 上提交 issue

---

**注意：** 本说明基于 ROS2 Foxy 和 CycloneDDS 最新稳定版本编写。 