#!/bin/bash

# CycloneDDS 快速开始脚本
# 一键安装、配置和测试

set -e

echo "=== CycloneDDS 快速开始 ==="
echo "本脚本将自动完成以下步骤："
echo "1. 安装 CycloneDDS"
echo "2. 配置环境变量"
echo "3. 创建配置文件"
echo "4. 运行测试"
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查是否为root用户
if [ "$EUID" -eq 0 ]; then
    print_warning "检测到root用户，建议使用普通用户运行此脚本"
fi

# 检查ROS2 Foxy
print_header "检查ROS2 Foxy安装"
if [ ! -d "/opt/ros/foxy" ]; then
    print_error "未找到ROS2 Foxy，请先安装ROS2 Foxy"
    echo "安装命令："
    echo "sudo apt update && sudo apt install ros-foxy-desktop"
    exit 1
fi
print_status "✓ ROS2 Foxy 已安装"

# 运行安装脚本
print_header "运行安装脚本"
if [ -f "./install_cyclonedds.sh" ]; then
    chmod +x ./install_cyclonedds.sh
    ./install_cyclonedds.sh
else
    print_error "安装脚本不存在"
    exit 1
fi

# 重新加载环境变量
print_header "重新加载环境变量"
source ~/.bashrc
source /opt/ros/foxy/setup.bash
export RMW_IMPLEMENTATION=rmw_cyclonedds_cpp
export CYCLONEDDS_URI=file:///tmp/cyclonedds.xml

# 运行测试脚本
print_header "运行测试脚本"
if [ -f "./test_cyclonedds.sh" ]; then
    chmod +x ./test_cyclonedds.sh
    ./test_cyclonedds.sh
else
    print_error "测试脚本不存在"
    exit 1
fi

# 演示基本功能
print_header "演示基本功能"
echo ""
echo "=== 演示：Talker-Listener 通信 ==="
echo "在接下来的演示中，您将看到："
echo "1. Talker节点发送消息"
echo "2. Listener节点接收消息"
echo "3. 按 Ctrl+C 停止演示"
echo ""

read -p "按回车键开始演示..." -r

# 启动talker
print_status "启动talker节点..."
ros2 run demo_nodes_cpp talker &
TALKER_PID=$!

sleep 2

# 启动listener
print_status "启动listener节点..."
ros2 run demo_nodes_cpp listener &
LISTENER_PID=$!

echo ""
echo "=== 演示进行中 ==="
echo "您应该能看到talker发送消息，listener接收消息"
echo "按 Ctrl+C 停止演示"
echo ""

# 等待用户中断
trap 'echo ""; print_status "停止演示..."; kill $TALKER_PID $LISTENER_PID 2>/dev/null; exit 0' INT
wait

print_status "演示完成！"
echo ""
echo "=== 快速开始完成 ==="
echo ""
echo "=== 常用命令 ==="
echo "1. 查看话题：ros2 topic list"
echo "2. 查看节点：ros2 node list"
echo "3. 查看服务：ros2 service list"
echo "4. 查看参数：ros2 param list"
echo ""
echo "=== 配置文件 ==="
echo "默认配置：/tmp/cyclonedds.xml"
echo "高性能配置：cyclonedds_configs/high_performance.xml"
echo "调试配置：cyclonedds_configs/debug.xml"
echo "多机器配置：cyclonedds_configs/multi_machine.xml"
echo ""
echo "=== 环境变量 ==="
echo "RMW_IMPLEMENTATION=rmw_cyclonedds_cpp"
echo "CYCLONEDDS_URI=file:///tmp/cyclonedds.xml"
echo ""
echo "=== 故障排除 ==="
echo "1. 检查安装：./test_cyclonedds.sh"
echo "2. 查看日志：export CYCLONEDDS_VERBOSITY=5"
echo "3. 重新安装：./install_cyclonedds.sh"
echo ""
print_status "CycloneDDS 已成功安装和配置！" 