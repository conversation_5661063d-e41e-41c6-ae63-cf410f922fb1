# CycloneDDS 在 ROS2 Foxy 上的安装和使用指南

## 概述

CycloneDDS 是一个高性能的 DDS (Data Distribution Service) 实现，专为 ROS2 设计。本指南将帮助您在 ROS2 Foxy 版本上安装、配置和使用 CycloneDDS。

## 系统要求

- Ubuntu 20.04 LTS
- ROS2 Foxy 已安装
- 至少 2GB 可用内存
- 网络连接（用于下载依赖）

## 1. 环境准备

### 1.1 更新系统包
```bash
sudo apt update && sudo apt upgrade -y
```

### 1.2 安装必要的依赖
```bash
sudo apt install -y \
    build-essential \
    cmake \
    git \
    wget \
    curl \
    libssl-dev \
    libcunit1-dev \
    libcunit1 \
    libcunit1-doc \
    libcunit1-dev \
    pkg-config \
    python3-pip \
    python3-setuptools \
    python3-wheel
```

## 2. 安装 CycloneDDS

### 2.1 方法一：从源码编译安装（推荐）

#### 2.1.1 克隆 CycloneDDS 仓库
```bash
cd ~
git clone https://github.com/eclipse-cyclonedds/cyclonedds.git
cd cyclonedds
```

#### 2.1.2 创建构建目录
```bash
mkdir build
cd build
```

#### 2.1.3 配置和编译
```bash
cmake -DCMAKE_INSTALL_PREFIX=/usr/local -DBUILD_EXAMPLES=ON ..
make -j$(nproc)
```

#### 2.1.4 安装
```bash
sudo make install
sudo ldconfig
```

### 2.2 方法二：使用包管理器安装
```bash
# 添加 CycloneDDS 官方仓库
wget -qO - https://packages.cyclonedds.org/keys/cyclonedds.asc | sudo apt-key add -
echo "deb https://packages.cyclonedds.org/ubuntu focal main" | sudo tee /etc/apt/sources.list.d/cyclonedds.list

# 更新包列表并安装
sudo apt update
sudo apt install -y cyclonedds
```

## 3. 配置 ROS2 使用 CycloneDDS

### 3.1 设置环境变量
将以下内容添加到您的 `~/.bashrc` 文件中：

```bash
# 设置 CycloneDDS 为默认 DDS
export RMW_IMPLEMENTATION=rmw_cyclonedds_cpp
export CYCLONEDDS_URI=file:///tmp/cyclonedds.xml
```

### 3.2 创建 CycloneDDS 配置文件
创建配置文件 `/tmp/cyclonedds.xml`：

```xml
<?xml version="1.0" encoding="UTF-8" ?>
<CycloneDDS xmlns="https://cdds.io/config" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="https://cdds.io/config https://raw.githubusercontent.com/eclipse-cyclonedds/cyclonedds/master/etc/cyclonedds.xsd">
    <Domain>
        <General>
            <NetworkInterfaceAddress>auto</NetworkInterfaceAddress>
            <AllowMulticast>true</AllowMulticast>
            <MaxMessageSize>65500B</MaxMessageSize>
            <FragmentSize>4000B</FragmentSize>
            <Watermarks>
                <WhcHigh>500kB</WhcHigh>
            </Watermarks>
        </General>
        <Tracing>
            <Verbosity>info</Verbosity>
            <OutputFile>stdout</OutputFile>
        </Tracing>
    </Domain>
</CycloneDDS>
```

### 3.3 重新加载环境
```bash
source ~/.bashrc
```

## 4. 验证安装

### 4.1 检查 CycloneDDS 安装
```bash
# 检查 CycloneDDS 库
ldconfig -p | grep cyclonedds

# 检查 ROS2 DDS 实现
ros2 doctor --report
```

### 4.2 测试基本功能
```bash
# 终端 1：启动 talker
source /opt/ros/foxy/setup.bash
export RMW_IMPLEMENTATION=rmw_cyclonedds_cpp
ros2 run demo_nodes_cpp talker

# 终端 2：启动 listener
source /opt/ros/foxy/setup.bash
export RMW_IMPLEMENTATION=rmw_cyclonedds_cpp
ros2 run demo_nodes_cpp listener
```

## 5. 高级配置

### 5.1 性能优化配置
创建高性能配置文件 `/tmp/cyclonedds_high_perf.xml`：

```xml
<?xml version="1.0" encoding="UTF-8" ?>
<CycloneDDS xmlns="https://cdds.io/config" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="https://cdds.io/config https://raw.githubusercontent.com/eclipse-cyclonedds/cyclonedds/master/etc/cyclonedds.xsd">
    <Domain>
        <General>
            <NetworkInterfaceAddress>auto</NetworkInterfaceAddress>
            <AllowMulticast>true</AllowMulticast>
            <MaxMessageSize>65500B</MaxMessageSize>
            <FragmentSize>4000B</FragmentSize>
            <Watermarks>
                <WhcHigh>500kB</WhcHigh>
            </Watermarks>
        </General>
        <Internal>
            <Watermarks>
                <WhcHigh>500kB</WhcHigh>
                <WhcLow>100kB</WhcLow>
                <WhcHunk>1MB</WhcHunk>
            </Watermarks>
        </Internal>
        <Tracing>
            <Verbosity>info</Verbosity>
            <OutputFile>stdout</OutputFile>
        </Tracing>
    </Domain>
</CycloneDDS>
```

### 5.2 网络配置
对于多机器部署，配置网络接口：

```xml
<NetworkInterfaceAddress>*************</NetworkInterfaceAddress>
```

## 6. 故障排除

### 6.1 常见问题

#### 问题：找不到 CycloneDDS 库
**解决方案：**
```bash
sudo ldconfig
echo $LD_LIBRARY_PATH
```

#### 问题：ROS2 无法使用 CycloneDDS
**解决方案：**
```bash
# 检查可用的 DDS 实现
ros2 doctor --report

# 确保环境变量正确设置
echo $RMW_IMPLEMENTATION
```

#### 问题：网络通信问题
**解决方案：**
```bash
# 检查防火墙设置
sudo ufw status

# 检查网络接口
ip addr show
```

### 6.2 调试技巧

#### 启用详细日志
```bash
export CYCLONEDDS_URI="file:///tmp/cyclonedds.xml"
export CYCLONEDDS_VERBOSITY=5
```

#### 检查 DDS 发现
```bash
# 使用 CycloneDDS 工具检查网络发现
cdds ls
```

## 7. 性能基准测试

### 7.1 延迟测试
```bash
# 安装性能测试工具
sudo apt install -y ros-foxy-performance-test

# 运行延迟测试
ros2 run performance_test performance_test --ros-args -p reliable:=true -p history_depth:=10
```

### 7.2 吞吐量测试
```bash
# 运行吞吐量测试
ros2 run performance_test performance_test --ros-args -p reliable:=true -p history_depth:=100
```

## 8. 最佳实践

### 8.1 配置建议
- 根据网络环境调整 `MaxMessageSize` 和 `FragmentSize`
- 在生产环境中禁用详细日志以提高性能
- 使用适当的 `Watermarks` 设置避免内存问题

### 8.2 监控
- 定期检查系统资源使用情况
- 监控网络延迟和丢包率
- 使用 `ros2 doctor` 定期检查系统健康状态

## 9. 更新和维护

### 9.1 更新 CycloneDDS
```bash
cd ~/cyclonedds
git pull
cd build
make -j$(nproc)
sudo make install
sudo ldconfig
```

### 9.2 清理
```bash
# 清理构建文件
cd ~/cyclonedds/build
make clean
```

## 10. 参考资源

- [CycloneDDS 官方文档](https://cyclonedds.io/docs/)
- [ROS2 DDS 指南](https://docs.ros.org/en/foxy/Guides/DDS-tuning.html)
- [CycloneDDS GitHub 仓库](https://github.com/eclipse-cyclonedds/cyclonedds)

---

**注意：** 本指南基于 ROS2 Foxy 和 CycloneDDS 最新稳定版本编写。如果您遇到特定问题，请参考官方文档或提交 issue。 