#!/bin/bash

# CycloneDDS 自动安装脚本
# 适用于 ROS2 Foxy

set -e  # 遇到错误时退出

echo "=== CycloneDDS 安装脚本 ==="
echo "开始安装 CycloneDDS..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
if [ "$EUID" -eq 0 ]; then
    print_warning "检测到root用户，建议使用普通用户运行此脚本"
fi

# 检查ROS2 Foxy是否安装
print_status "检查ROS2 Foxy安装..."
if [ ! -d "/opt/ros/foxy" ]; then
    print_error "未找到ROS2 Foxy，请先安装ROS2 Foxy"
    exit 1
fi

# 更新系统包
print_status "更新系统包..."
sudo apt update && sudo apt upgrade -y

# 安装依赖
print_status "安装必要的依赖包..."
sudo apt install -y \
    build-essential \
    cmake \
    git \
    wget \
    curl \
    libssl-dev \
    libcunit1-dev \
    libcunit1 \
    libcunit1-doc \
    pkg-config \
    python3-pip \
    python3-setuptools \
    python3-wheel \
    ninja-build

# 检查是否已安装CycloneDDS
if ldconfig -p | grep -q cyclonedds; then
    print_warning "检测到已安装的CycloneDDS，是否重新安装？(y/N)"
    read -r response
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        print_status "跳过安装，继续配置..."
    else
        print_status "重新安装CycloneDDS..."
    fi
fi

# 克隆CycloneDDS仓库
print_status "克隆CycloneDDS仓库..."
if [ ! -d "$HOME/cyclonedds" ]; then
    cd ~
    git clone https://github.com/eclipse-cyclonedds/cyclonedds.git
    cd cyclonedds
else
    print_status "CycloneDDS仓库已存在，更新..."
    cd ~/cyclonedds
    git pull
fi

# 创建构建目录
print_status "配置和编译CycloneDDS..."
mkdir -p build
cd build

# 配置CMake
cmake -DCMAKE_INSTALL_PREFIX=/usr/local \
      -DBUILD_EXAMPLES=ON \
      -DBUILD_SHARED_LIBS=ON \
      -DCMAKE_BUILD_TYPE=Release \
      ..

# 编译
print_status "编译CycloneDDS (这可能需要几分钟)..."
make -j$(nproc)

# 安装
print_status "安装CycloneDDS..."
sudo make install
sudo ldconfig

print_status "CycloneDDS 安装完成！"

# 创建配置文件
print_status "创建CycloneDDS配置文件..."
sudo tee /tmp/cyclonedds.xml > /dev/null << 'EOF'
<?xml version="1.0" encoding="UTF-8" ?>
<CycloneDDS xmlns="https://cdds.io/config" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="https://cdds.io/config https://raw.githubusercontent.com/eclipse-cyclonedds/cyclonedds/master/etc/cyclonedds.xsd">
    <Domain>
        <General>
            <NetworkInterfaceAddress>auto</NetworkInterfaceAddress>
            <AllowMulticast>true</AllowMulticast>
            <MaxMessageSize>65500B</MaxMessageSize>
            <FragmentSize>4000B</FragmentSize>
            <Watermarks>
                <WhcHigh>500kB</WhcHigh>
            </Watermarks>
        </General>
        <Tracing>
            <Verbosity>info</Verbosity>
            <OutputFile>stdout</OutputFile>
        </Tracing>
    </Domain>
</CycloneDDS>
EOF

# 创建高性能配置文件
print_status "创建高性能配置文件..."
sudo tee /tmp/cyclonedds_high_perf.xml > /dev/null << 'EOF'
<?xml version="1.0" encoding="UTF-8" ?>
<CycloneDDS xmlns="https://cdds.io/config" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="https://cdds.io/config https://raw.githubusercontent.com/eclipse-cyclonedds/cyclonedds/master/etc/cyclonedds.xsd">
    <Domain>
        <General>
            <NetworkInterfaceAddress>auto</NetworkInterfaceAddress>
            <AllowMulticast>true</AllowMulticast>
            <MaxMessageSize>65500B</MaxMessageSize>
            <FragmentSize>4000B</FragmentSize>
            <Watermarks>
                <WhcHigh>500kB</WhcHigh>
            </Watermarks>
        </General>
        <Internal>
            <Watermarks>
                <WhcHigh>500kB</WhcHigh>
                <WhcLow>100kB</WhcLow>
                <WhcHunk>1MB</WhcHunk>
            </Watermarks>
        </Internal>
        <Tracing>
            <Verbosity>info</Verbosity>
            <OutputFile>stdout</OutputFile>
        </Tracing>
    </Domain>
</CycloneDDS>
EOF

# 配置环境变量
print_status "配置环境变量..."
if ! grep -q "RMW_IMPLEMENTATION=rmw_cyclonedds_cpp" ~/.bashrc; then
    echo "" >> ~/.bashrc
    echo "# CycloneDDS 配置" >> ~/.bashrc
    echo "export RMW_IMPLEMENTATION=rmw_cyclonedds_cpp" >> ~/.bashrc
    echo "export CYCLONEDDS_URI=file:///tmp/cyclonedds.xml" >> ~/.bashrc
    echo "export CYCLONEDDS_VERBOSITY=1" >> ~/.bashrc
fi

# 验证安装
print_status "验证安装..."
if ldconfig -p | grep -q cyclonedds; then
    print_status "✓ CycloneDDS 库安装成功"
else
    print_error "✗ CycloneDDS 库安装失败"
    exit 1
fi

# 测试ROS2集成
print_status "测试ROS2集成..."
source /opt/ros/foxy/setup.bash
export RMW_IMPLEMENTATION=rmw_cyclonedds_cpp

# 检查ROS2 DDS实现
if ros2 doctor --report | grep -q "rmw_cyclonedds_cpp"; then
    print_status "✓ ROS2 CycloneDDS 集成成功"
else
    print_warning "⚠ ROS2 CycloneDDS 集成可能需要额外配置"
fi

print_status "安装完成！"
echo ""
echo "=== 下一步操作 ==="
echo "1. 重新加载环境变量："
echo "   source ~/.bashrc"
echo ""
echo "2. 测试CycloneDDS："
echo "   # 终端1: ros2 run demo_nodes_cpp talker"
echo "   # 终端2: ros2 run demo_nodes_cpp listener"
echo ""
echo "3. 查看详细文档："
echo "   cat README.md"
echo ""
echo "=== 配置文件位置 ==="
echo "默认配置: /tmp/cyclonedds.xml"
echo "高性能配置: /tmp/cyclonedds_high_perf.xml"
echo ""
echo "=== 环境变量 ==="
echo "RMW_IMPLEMENTATION=rmw_cyclonedds_cpp"
echo "CYCLONEDDS_URI=file:///tmp/cyclonedds.xml"
echo "CYCLONEDDS_VERBOSITY=1" 