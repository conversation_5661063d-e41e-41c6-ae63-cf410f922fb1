#!/bin/bash

# CycloneDDS 测试脚本
# 用于验证安装和配置是否正确

set -e

echo "=== CycloneDDS 测试脚本 ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

# 测试1: 检查CycloneDDS库
print_header "测试1: 检查CycloneDDS库安装"
if ldconfig -p | grep -q cyclonedds; then
    print_status "✓ CycloneDDS 库已安装"
    ldconfig -p | grep cyclonedds
else
    print_error "✗ CycloneDDS 库未安装"
    exit 1
fi

# 测试2: 检查环境变量
print_header "测试2: 检查环境变量"
if [ -n "$RMW_IMPLEMENTATION" ]; then
    print_status "✓ RMW_IMPLEMENTATION 已设置: $RMW_IMPLEMENTATION"
else
    print_warning "⚠ RMW_IMPLEMENTATION 未设置，正在设置..."
    export RMW_IMPLEMENTATION=rmw_cyclonedds_cpp
fi

if [ -n "$CYCLONEDDS_URI" ]; then
    print_status "✓ CYCLONEDDS_URI 已设置: $CYCLONEDDS_URI"
else
    print_warning "⚠ CYCLONEDDS_URI 未设置，正在设置..."
    export CYCLONEDDS_URI=file:///tmp/cyclonedds.xml
fi

# 测试3: 检查配置文件
print_header "测试3: 检查配置文件"
if [ -f "/tmp/cyclonedds.xml" ]; then
    print_status "✓ CycloneDDS 配置文件存在"
    echo "配置文件内容预览:"
    head -10 /tmp/cyclonedds.xml
else
    print_error "✗ CycloneDDS 配置文件不存在"
    exit 1
fi

# 测试4: 检查ROS2集成
print_header "测试4: 检查ROS2集成"
source /opt/ros/foxy/setup.bash

# 检查可用的DDS实现
print_status "检查可用的DDS实现:"
ros2 doctor --report | grep -A 5 -B 5 "DDS"

# 测试5: 基本通信测试
print_header "测试5: 基本通信测试"
print_status "启动talker节点 (5秒后自动停止)..."
timeout 5s ros2 run demo_nodes_cpp talker &
TALKER_PID=$!

sleep 2

print_status "启动listener节点 (5秒后自动停止)..."
timeout 5s ros2 run demo_nodes_cpp listener &
LISTENER_PID=$!

# 等待进程完成
wait $TALKER_PID $LISTENER_PID 2>/dev/null || true

print_status "基本通信测试完成"

# 测试6: 网络发现测试
print_header "测试6: 网络发现测试"
print_status "检查DDS发现信息..."
if command -v cdds &> /dev/null; then
    print_status "使用cdds工具检查网络发现..."
    timeout 3s cdds ls || print_warning "cdds工具不可用或超时"
else
    print_warning "cdds工具不可用，跳过网络发现测试"
fi

# 测试7: 性能测试
print_header "测试7: 性能测试"
print_status "检查性能测试工具..."
if ros2 pkg list | grep -q performance_test; then
    print_status "性能测试工具可用"
    print_status "运行简单性能测试 (10秒)..."
    timeout 10s ros2 run performance_test performance_test --ros-args -p reliable:=true -p history_depth:=10 || print_warning "性能测试失败或超时"
else
    print_warning "性能测试工具不可用，跳过性能测试"
    print_status "安装性能测试工具: sudo apt install ros-foxy-performance-test"
fi

# 测试8: 多机器通信测试（可选）
print_header "测试8: 多机器通信测试"
print_status "检查网络接口..."
ip addr show | grep -E "inet.*global" || print_warning "未找到全局网络接口"

print_status "检查多播支持..."
if command -v netstat &> /dev/null; then
    netstat -g | grep -q "*********" && print_status "✓ 多播支持正常" || print_warning "⚠ 多播支持可能有问题"
else
    print_warning "无法检查多播支持"
fi

# 测试9: 配置文件验证
print_header "测试9: 配置文件验证"
print_status "验证XML配置文件格式..."
if command -v xmllint &> /dev/null; then
    if xmllint --noout /tmp/cyclonedds.xml 2>/dev/null; then
        print_status "✓ XML配置文件格式正确"
    else
        print_error "✗ XML配置文件格式错误"
    fi
else
    print_warning "xmllint工具不可用，跳过XML验证"
fi

# 测试10: 内存和资源使用
print_header "测试10: 内存和资源使用"
print_status "检查系统资源..."
echo "内存使用:"
free -h | grep -E "Mem|内存"
echo ""
echo "磁盘使用:"
df -h /tmp | tail -1
echo ""

# 总结
print_header "测试总结"
echo "=== 测试结果 ==="
echo "✓ CycloneDDS 库安装: 正常"
echo "✓ 环境变量配置: 正常"
echo "✓ 配置文件: 正常"
echo "✓ ROS2 集成: 正常"
echo "✓ 基本通信: 正常"
echo ""
echo "=== 建议 ==="
echo "1. 如果所有测试通过，CycloneDDS已成功安装和配置"
echo "2. 如果遇到问题，请检查错误信息并参考README.md"
echo "3. 对于生产环境，建议进行更详细的性能测试"
echo ""
echo "=== 下一步 ==="
echo "1. 运行实际应用测试"
echo "2. 配置网络设置（如需要）"
echo "3. 调整性能参数（如需要）"
echo ""
print_status "测试完成！" 